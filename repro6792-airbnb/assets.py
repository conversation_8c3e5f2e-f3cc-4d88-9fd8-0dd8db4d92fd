"""
Shared assets for Strands Agent demo workflows.

This module contains common setup code used across multiple demo examples:
- Hook implementations for error handling and metrics
- LLM model initialization helpers
- OpenTelemetry and Braintrust logging configuration

Purpose: Reduce code duplication across demo examples while keeping
         agent-specific logic in individual demo files.
"""

import logging
import os
import json

from opentelemetry import metrics, trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter, SpanExporter, SpanExportResult
from opentelemetry.trace import Status, StatusCode
from strands.experimental.hooks.events import AfterModelInvocationEvent
from strands.hooks import HookProvider, HookRegistry
from strands.hooks.events import MessageAddedEvent
from strands.models.litellm import LiteLLMModel
from strands.telemetry import StrandsTelemetry

# Set up logging
logger = logging.getLogger(__name__)

# Set up OpenTelemetry metrics
meter = metrics.get_meter(__name__)
model_invocation_error = meter.create_counter("maestro_model_invocation_error", unit="1")


# ============================================================================
# DEBUGGING SPAN EXPORTER
# ============================================================================


class DebuggingSpanExporter(SpanExporter):
    """
    A debugging span exporter that logs detailed information about spans
    before forwarding them to the actual OTLP exporter.
    """

    def __init__(self, otlp_exporter: OTLPSpanExporter):
        self.otlp_exporter = otlp_exporter
        self.debug_logger = logging.getLogger(f"{__name__}.debugging_exporter")

    def export(self, spans):
        """Export spans with detailed logging."""
        self.debug_logger.info(f"=== DEBUGGING EXPORTER: Exporting {len(spans)} spans ===")

        for i, span in enumerate(spans):
            self.debug_logger.info(f"--- Span {i+1}/{len(spans)} ---")
            self.debug_logger.info(f"Span Name: {span.name}")
            self.debug_logger.info(f"Span ID: {format(span.context.span_id, '016x')}")
            self.debug_logger.info(f"Trace ID: {format(span.context.trace_id, '032x')}")

            # Log span attributes
            if span.attributes:
                self.debug_logger.info(f"Attributes count: {len(span.attributes)}")
                for key, value in span.attributes.items():
                    value_str = str(value)
                    if len(value_str) > 200:
                        value_str = value_str[:200] + "... (truncated)"
                    self.debug_logger.info(f"  {key}: {value_str}")

            # Log span events (this is where gen_ai.choice should appear)
            if span.events:
                self.debug_logger.info(f"Events count: {len(span.events)}")
                for j, event in enumerate(span.events):
                    self.debug_logger.info(f"  Event {j+1}: {event.name}")
                    if event.attributes:
                        for key, value in event.attributes.items():
                            value_str = str(value)
                            if len(value_str) > 500:
                                self.debug_logger.info(f"    {key}: {value_str[:500]}... (truncated, full length: {len(value_str)})")
                            else:
                                self.debug_logger.info(f"    {key}: {value_str}")
            else:
                self.debug_logger.info("No events in this span")

            # Calculate total span data size
            span_size = self._estimate_span_size(span)
            self.debug_logger.info(f"Estimated span size: {span_size} bytes")

            self.debug_logger.info("--- End Span ---")

        # Forward to actual OTLP exporter
        try:
            result = self.otlp_exporter.export(spans)
            self.debug_logger.info(f"OTLP Export Result: {result}")
            return result
        except Exception as e:
            self.debug_logger.error(f"OTLP Export Error: {e}")
            return SpanExportResult.FAILURE

    def shutdown(self):
        """Shutdown the exporter."""
        return self.otlp_exporter.shutdown()

    def force_flush(self, timeout_millis: int = 30000):
        """Force flush the exporter."""
        return self.otlp_exporter.force_flush(timeout_millis)

    def _estimate_span_size(self, span):
        """Estimate the size of a span in bytes."""
        size = 0

        # Name
        size += len(span.name.encode('utf-8'))

        # Attributes
        if span.attributes:
            for key, value in span.attributes.items():
                size += len(str(key).encode('utf-8'))
                size += len(str(value).encode('utf-8'))

        # Events
        if span.events:
            for event in span.events:
                size += len(event.name.encode('utf-8'))
                if event.attributes:
                    for key, value in event.attributes.items():
                        size += len(str(key).encode('utf-8'))
                        size += len(str(value).encode('utf-8'))

        return size


# ============================================================================
# HOOK IMPLEMENTATION
# ============================================================================


class FailFastOnToolError(HookProvider):
    """
    A hook that fails the agent immediately if any tool returns an error status.
    This is to prevent the agent from continuing when any tool has failed.
    """

    def register_hooks(self, registry: HookRegistry) -> None:
        registry.add_callback(AfterModelInvocationEvent, self._on_after_model_invocation_event)
        registry.add_callback(MessageAddedEvent, self._on_message)

    def _on_message(self, event: MessageAddedEvent) -> None:
        # MessageAddedEvent fires whenever the agent appends a message to history.
        # We watch for toolResult blocks and fail on the first error.
        for block in event.message.get("content", []):
            tr = block.get("toolResult")
            if tr and tr.get("status") == "error":
                # Surface any text payload from the tool as the exception message
                details = next((c.get("text") for c in tr.get("content", []) if "text" in c), "")
                raise RuntimeError(f"Tool failure: {details or 'no details'}")

    def _on_after_model_invocation_event(self, event: AfterModelInvocationEvent) -> None:
        """
        This hook is called after every model invocation, whether successful or
        failed. Add a metric tracking exception types.
        """
        if e := event.exception:
            model_invocation_error.add(
                1, attributes={"exception_type": f"{type(e).__module__}.{type(e).__name__}"}
            )
            trace.get_current_span().record_exception(e)


# ============================================================================
# LLM INITIALIZATION HELPER
# ============================================================================


def setup_llm_model(model_id: str) -> LiteLLMModel:
    """
    Initialize and return a LiteLLM model.

    For Airbnb developers: Uses airbnb_litellm.initialize()
    For external users: Comment out the initialize() call and ensure your API keys are in .env

    Args:
        model_id: The model ID to use

    Returns:
        LiteLLMModel: Configured model instance
    """

    # Comment out the next 2 lines if you're not using Airbnb-specific setup
    # from airbnb_litellm import initialize

    # initialize(model_id)

    return LiteLLMModel(model_id=model_id)


# ============================================================================
# OPENTELEMETRY AND BRAINTRUST INITIALIZATION
# BRAINTRUST_PROJECT_ID, BRAINTRUST_API_KEY, OTEL_EXPORTER_OTLP_ENDPOINT should
# be included in environment variables
# ============================================================================


def opentelemetry_init(console: bool = True, debug_export: bool = True):
    """
    Set up logging with OpenTelemetry and Braintrust.

    This function configures:
    - OpenTelemetry tracer provider with OTLP exporter
    - Optional console exporter for local debugging
    - Strands telemetry integration
    - Optional debugging exporter to trace data flow

    Args:
        console: If True, also export traces to console for debugging
        debug_export: If True, use debugging exporter to log span details

    Environment variables required:
        BRAINTRUST_PROJECT_ID: Your Braintrust project ID
        BRAINTRUST_API_KEY: Your Braintrust API key
        OTEL_EXPORTER_OTLP_ENDPOINT: OTLP endpoint URL (default: https://api.braintrust.dev/otel/v1/traces)
    """
    braintrust_project_id = '5fd85d16-a75c-41fc-9d29-c1a56a6fcb2f'
    braintrust_api_key = os.getenv("BRAINTRUST_API_KEY")

    bt_parent = f"project_id:{braintrust_project_id}"
    headers = {
        "authorization": f"bearer {braintrust_api_key}",
        "x-bt-parent": bt_parent,
    }

    resource = Resource(attributes={})
    tracer_provider = TracerProvider(resource=resource)

    # Create OTLP exporter
    otlp_exporter = OTLPSpanExporter(endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"), headers=headers)

    # Wrap with debugging exporter if requested
    if debug_export:
        logger.info("Using debugging exporter to trace span data flow")
        exporter = DebuggingSpanExporter(otlp_exporter)
    else:
        exporter = otlp_exporter

    processor = BatchSpanProcessor(exporter)
    tracer_provider.add_span_processor(processor)

    if console:
        console_processor = BatchSpanProcessor(ConsoleSpanExporter())
        tracer_provider.add_span_processor(console_processor)

    trace.set_tracer_provider(tracer_provider)

    # Set up strands telemetry
    logger.info("Setting up Strands telemetry...")
    strands_telemetry = StrandsTelemetry()
    strands_telemetry.setup_otlp_exporter(
        endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"), headers=headers
    )
    logger.info("Strands OTLP exporter configured")

    if console:
        strands_telemetry.setup_console_exporter()
        logger.info("Strands console exporter configured")
