aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
APScheduler==3.11.0
attrs==25.3.0
autoevals==0.0.129
aws-requests-auth==0.4.3
azure-core==1.35.1
azure-identity==1.25.1
azure-storage-blob==12.26.0
backoff==2.2.1
beautifulsoup4==4.14.2
boto3==1.36.0
botocore==1.36.26
braintrust==0.2.9
braintrust_core==0.0.59
cachetools==5.5.2
certifi==2025.6.15
cffi==2.0.0
charset-normalizer==3.4.2
chevron==0.14.0
click==8.3.0
croniter==6.0.0
cryptography==43.0.3
datasets==3.6.0
distro==1.9.0
dnspython==2.8.0
docstring_parser==0.17.0
email-validator==2.3.0
exceptiongroup==1.3.0
fastapi==0.115.14
fastapi-sso==0.16.0
filelock==3.18.0
frozenlist==1.7.0
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
google-api-core==2.25.1
google-auth==2.40.3
google-cloud-aiplatform==1.107.0
google-cloud-bigquery==3.35.1
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-storage==2.19.0
google-crc32c==1.7.1
google-genai==1.29.0
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
grpc-google-iam-v1==0.14.2
grpcio==1.74.0
grpcio-status==1.74.0
gunicorn==23.0.0
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.2
huggingface-hub==0.33.0
idna==3.10
importlib_metadata==8.7.0
isodate==0.7.2
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
litellm==1.75.9
litellm-enterprise==0.1.19
litellm-proxy-extras==0.2.17
lxml==5.4.0
markdown-it-py==4.0.0
markdownify==1.2.0
MarkupSafe==3.0.3
mcp==1.12.4
mdurl==0.1.2
metrics==0.0.2
mpmath==1.3.0
msal==1.34.0
msal-extensions==1.3.1
multidict==6.5.0
multiprocess==0.70.16
numpy==2.3.1
oauthlib==3.3.1
openai==2.2.0
opentelemetry-api==1.37.0
opentelemetry-exporter-otlp-proto-common==1.37.0
opentelemetry-exporter-otlp-proto-grpc==1.37.0
opentelemetry-exporter-otlp-proto-http==1.37.0
opentelemetry-instrumentation==0.58b0
opentelemetry-instrumentation-flask==0.56b0
opentelemetry-instrumentation-threading==0.58b0
opentelemetry-instrumentation-wsgi==0.56b0
opentelemetry-proto==1.37.0
opentelemetry-sdk==1.37.0
opentelemetry-semantic-conventions==0.58b0
opentelemetry-util-http==0.56b0
orjson==3.11.3
packaging==25.0
pandas==2.3.0
pillow==11.2.1
polars==1.34.0
polars-runtime-32==1.34.0
polyleven==0.9.0
prompt_toolkit==3.0.52
propcache==0.3.2
proto-plus==1.26.1
protobuf==6.31.1
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.23
pydantic==2.11.9
pydantic-settings==2.11.0
pydantic_core==2.33.2
Pygments==2.19.2
PyJWT==2.10.1
PyNaCl==1.6.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-multipart==0.0.18
python-slugify==8.0.4
pytz==2025.2
PyYAML==6.0.2
redis==6.4.0
referencing==0.36.2
regex==2025.9.18
requests==2.32.4
rich==13.7.1
rpds-py==0.25.1
rq==2.6.0
rsa==4.9.1
s3transfer==0.11.3
shapely==2.1.1
six==1.17.0
slack_bolt==1.26.0
slack_sdk==3.37.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.8
sse-starlette==3.0.2
sseclient-py==1.8.0
starlette==0.46.2
strands-agents==1.5.0
strands-agents-tools==0.2.9
sympy==1.14.0
tenacity==9.1.2
text-unidecode==1.3
tiktoken==0.12.0
tokenizers==0.22.1
tqdm==4.67.1
twelvelabs==0.4.10
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.5.0
uvicorn==0.29.0
uvloop==0.21.0
watchdog==6.0.0
wcwidth==0.2.14
websockets==13.1
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
