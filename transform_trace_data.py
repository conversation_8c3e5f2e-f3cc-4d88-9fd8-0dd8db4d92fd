#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to transform hierarchical trace data from 'repro' format to flat 'result' format.

This script takes JSON files with hierarchical tree structures (like assort_repro.json)
and converts them to flat event arrays (like assort_result.json), while excluding
specific keys like _xact_id, _pagination_key, log_id, and project_id.
"""

import json
import sys
from typing import Dict, List, Any, Set
from pathlib import Path


def should_exclude_key(key: str, exclude_keys: Set[str]) -> bool:
    """Check if a key should be excluded from the output."""
    return key in exclude_keys


def clean_event_data(data: Dict[str, Any], exclude_keys: Set[str]) -> Dict[str, Any]:
    """Clean event data by removing excluded keys."""
    cleaned = {}
    for key, value in data.items():
        if not should_exclude_key(key, exclude_keys):
            cleaned[key] = value
    return cleaned


def flatten_trace_tree(node: Dict[str, Any], exclude_keys: Set[str]) -> List[Dict[str, Any]]:
    """
    Recursively flatten a hierarchical trace tree into a list of events.
    
    Args:
        node: The current node in the tree (can be root or child)
        exclude_keys: Set of keys to exclude from the output
        
    Returns:
        List of flattened events
    """
    events = []
    
    # If this node has data, add it as an event
    if 'data' in node:
        event_data = clean_event_data(node['data'], exclude_keys)
        events.append(event_data)
    
    # Recursively process children
    if 'children' in node and isinstance(node['children'], list):
        for child in node['children']:
            child_events = flatten_trace_tree(child, exclude_keys)
            events.extend(child_events)
    
    return events


def transform_repro_to_result(input_file: str, output_file: str, exclude_keys: Set[str] = None) -> None:
    """
    Transform a repro file to result format.
    
    Args:
        input_file: Path to input repro JSON file
        output_file: Path to output result JSON file
        exclude_keys: Set of keys to exclude from output
    """
    if exclude_keys is None:
        exclude_keys = {'_xact_id', '_pagination_key', 'log_id', 'project_id'}
    
    try:
        # Read input file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Flatten the tree structure
        events = flatten_trace_tree(data, exclude_keys)
        
        # Create result structure
        result = {
            "events": events
        }
        
        # Write output file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully transformed {input_file} -> {output_file}")
        print(f"Generated {len(events)} events")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input file '{input_file}': {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error processing files: {e}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        print("Usage: python transform_trace_data.py <input_file> [output_file] [--exclude-keys key1,key2,...]")
        print("")
        print("Examples:")
        print("  python transform_trace_data.py assort_repro.json")
        print("  python transform_trace_data.py assort_repro.json assort_result.json")
        print("  python transform_trace_data.py data.json output.json --exclude-keys _xact_id,log_id")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    # Determine output file name
    if len(sys.argv) >= 3 and not sys.argv[2].startswith('--'):
        output_file = sys.argv[2]
    else:
        # Generate output filename by replacing 'repro' with 'result'
        input_path = Path(input_file)
        if 'repro' in input_path.stem:
            output_name = input_path.stem.replace('repro', 'result') + input_path.suffix
            output_file = input_path.parent / output_name
        else:
            output_file = input_path.parent / (input_path.stem + '_result' + input_path.suffix)
    
    # Parse exclude keys from command line
    exclude_keys = {'_xact_id', '_pagination_key', 'log_id', 'project_id'}
    
    for i, arg in enumerate(sys.argv):
        if arg == '--exclude-keys' and i + 1 < len(sys.argv):
            exclude_keys = set(sys.argv[i + 1].split(','))
            break
    
    # Transform the file
    transform_repro_to_result(input_file, str(output_file), exclude_keys)


if __name__ == "__main__":
    main()





